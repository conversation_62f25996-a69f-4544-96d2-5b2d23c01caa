{"name": "functions", "scripts": {"lint": "eslint --ext .js,.ts .", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "20"}, "main": "lib/index.js", "dependencies": {"@azure/identity": "^4.10.1", "@resvg/resvg-js": "^2.6.2", "busboy": "^1.6.0", "firebase-admin": "^13.2.0", "firebase-functions": "^6.1.0", "microsoft-cognitiveservices-speech-sdk": "^1.44.1", "qrcode-svg": "^1.1.0", "sharp": "^0.34.3", "ulid": "^3.0.1"}, "devDependencies": {"@types/busboy": "^1.5.4", "@types/qrcode-svg": "^1.1.5", "@types/sharp": "^0.31.1", "@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "eslint": "^8.9.0", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.25.4", "firebase-functions-test": "^3.1.0", "typescript": "^4.9.0"}, "private": true}